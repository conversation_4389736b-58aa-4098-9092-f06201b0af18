/* 成品测试查询页面样式 */
.product-test-query {
    width: 100%;
}

/* 测试结果标签 */
.product-test-query__result-tag {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 14px;
}

.product-test-query__result-tag--pass {
    background-color: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
}

.product-test-query__result-tag--fail {
    background-color: #fff1f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
}

/* 模态框 */
.product-test-query__modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    overflow: hidden;
}

.product-test-query__modal-content {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    width: 90%;
    max-width: 1200px;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    resize: both;
    overflow: auto;
    margin: 0;
    transition: none !important;
    animation: none !important;
}

.product-test-query__modal-header {
    padding: 16px 24px;
    background: #f7f7f7;
    border-radius: 8px 8px 0 0;
    cursor: move;
    user-select: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #eee;
    position: sticky;
    top: 0;
    z-index: 2;
}

.product-test-query__modal-title {
    margin: 0;
    font-size: 18px;
    color: #333;
    font-weight: 500;
}

.product-test-query__modal-close {
    font-size: 24px;
    color: #999;
    cursor: pointer;
    padding: 4px;
    line-height: 1;
}

.product-test-query__modal-close:hover {
    color: #666;
}

.product-test-query__modal-body {
    padding: 24px;
    overflow-y: auto;
    flex: 1;
    min-height: 300px;
    max-height: calc(80vh - 120px);
}

/* 信息卡片 */
.product-test-query__info-card {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 24px;
    border: 1px solid #eee;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    transition: box-shadow 0.2s ease;
}

.product-test-query__info-card:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
}

.product-test-query__info-card--device {
    background: #fafafa;
}

.product-test-query__card-title {
    font-size: 16px;
    font-weight: 600;
    color: #1890ff;
    margin: 0 0 16px 0;
    padding-bottom: 12px;
    border-bottom: 2px solid #e6f4ff;
    position: relative;
}

.product-test-query__card-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 40px;
    height: 2px;
    background-color: #1890ff;
}

.product-test-query__info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    align-items: start;
}

.product-test-query__info-item {
    display: flex;
    flex-direction: column;
    min-width: 0;
}

.product-test-query__info-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 4px;
}

.product-test-query__info-value {
    font-size: 14px;
    color: #333;
    word-break: break-all;
    line-height: 1.5;
}

/* 测试结果网格 */
.product-test-query__test-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 16px;
    margin-top: 16px;
}

.product-test-query__test-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f5f5f5;
    border-radius: 4px;
}

.product-test-query__test-label {
    font-size: 14px;
    color: #666;
    margin-right: 8px;
}

.product-test-query__test-value {
    font-weight: 500;
    min-width: 48px;
    text-align: center;
}

.product-test-query__test-value--pass {
    color: #52c41a;
}

.product-test-query__test-value--fail {
    color: #f5222d;
}

/* 操作按钮 */
.product-test-query__btn-detail {
    padding: 6px 12px;
    background: #1890ff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.product-test-query__btn-detail:hover {
    background: #40a9ff;
    box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

.product-test-query__btn-detail i {
    font-size: 14px;
}

/* 拖动状态 */
.product-test-query__modal-content--dragging {
    opacity: 0.95;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
    cursor: move;
    user-select: none;
}

/* 调整大小手柄 */
.product-test-query__modal-content::after {
    content: '';
    position: absolute;
    right: 0;
    bottom: 0;
    width: 16px;
    height: 16px;
    cursor: se-resize;
    background: linear-gradient(135deg, transparent 50%, #ddd 50%);
}

/* 响应式调整 */
@media screen and (max-width: 1200px) {
    .product-test-query__modal-content {
        width: 95%;
        margin: 10px;
    }

    .product-test-query__info-grid {
        grid-template-columns: 1fr;
    }

    .product-test-query__test-grid {
        grid-template-columns: 1fr;
    }
}

/* 滚动条样式 */
.product-test-query__modal-body::-webkit-scrollbar {
    width: 8px;
}

.product-test-query__modal-body::-webkit-scrollbar-track {
    background: #f5f5f5;
    border-radius: 4px;
}

.product-test-query__modal-body::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 4px;
}

.product-test-query__modal-body::-webkit-scrollbar-thumb:hover {
    background: #999;
}

/* 移除其他可能的过渡效果 */
.product-test-query__modal *,
.product-test-query__modal-content * {
    transition: none !important;
    animation: none !important;
    transform-style: flat !important;
}

.product-test-query__info-item--empty {
    grid-column: 1 / -1;  /* 跨越所有列 */
    text-align: center;
    color: #999;
    padding: 20px;
}

/* 添加主题变量 */
#product-test-query-content {
    /* 主题变量 - 借鉴自BatchQuery的设计系统 */
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;
    --radius: 0.5rem;

    /* 添加字体相关变量 */
    font-family: -apple-system, BlinkMacSystemFont, 'Inter', sans-serif;
}

/* 保持原有样式，修改颜色和字体大小 */
#product-test-query-content .query-section {
    background-color: hsl(var(--card));
    padding: 24px;
    border-radius: var(--radius);
    border: 1px solid hsl(var(--border));
    margin-bottom: 24px;
}

#product-test-query-content .query-form {
    display: flex;
    flex-wrap: nowrap;
    gap: 16px;
    margin-bottom: 28px;
    align-items: flex-end;
}

#product-test-query-content .form-item {
    flex: 1;
    min-width: 0;
    margin: 0;
}

#product-test-query-content .form-item:nth-child(3) {
    flex: 0.7;
}

#product-test-query-content .form-item:nth-child(4),
#product-test-query-content .form-item:nth-child(5) {
    flex: 0.9;
}

#product-test-query-content .form-input,
#product-test-query-content .form-select {
    width: 100%;
    padding: 10px 14px;
    background-color: transparent;
    border: 1px solid hsl(var(--input));
    border-radius: var(--radius);
    font-size: 14px;
    color: hsl(var(--foreground));
    transition: all 0.2s ease;
    min-width: 120px;
}

#product-test-query-content .form-input:hover,
#product-test-query-content .form-select:hover {
    border-color: hsl(var(--ring));
}

#product-test-query-content .form-input:focus,
#product-test-query-content .form-select:focus {
    outline: none;
    border-color: hsl(var(--ring));
    box-shadow: 0 0 0 2px hsl(var(--background)), 0 0 0 4px hsl(var(--ring)/0.1);
}

#product-test-query-content .btn {
    font-size: 14px;
    font-weight: 500;
}

#product-test-query-content .btn-primary {
    background-color: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
}

#product-test-query-content .btn-default {
    background-color: hsl(var(--secondary));
    color: hsl(var(--secondary-foreground));
}

#product-test-query-content .table-header th {
    background-color: hsl(var(--muted));
    color: hsl(var(--muted-foreground));
    border-color: hsl(var(--border));
    font-size: 14px;
    font-weight: 500;
    padding: 12px 16px;
}

#product-test-query-content .table-row td {
    border-color: hsl(var(--border));
    color: hsl(var(--foreground));
    font-size: 14px;
    padding: 12px 16px;
}

#product-test-query-content .page-button {
    padding: 6px 12px;
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    background-color: hsl(var(--card));  /* 添加默认背景色 */
    color: hsl(var(--foreground));
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

#product-test-query-content .page-button:hover:not(:disabled) {
    background-color: hsl(var(--accent));  /* 添加悬停背景色 */
}

#product-test-query-content .page-button.active {
    background-color: hsl(var(--primary));  /* 当前页码的背景色 */
    color: hsl(var(--primary-foreground));
    border-color: transparent;
}

#product-test-query-content .page-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

#product-test-query-content .page-input,
#product-test-query-content .pagination-section .page-input {
    width: 40px !important;  /* 保持原来的页码输入框宽度 */
    text-align: center;
    padding: 4px 8px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
}

#product-test-query-content .page-size select.form-select {
    width: auto !important;  /* 让选择框宽度自适应内容 */
    min-width: 60px;
    text-align: center;
    padding: 4px 8px;
}

#product-test-query-content .query-buttons {
    display: flex;
    gap: 8px;
    margin-left: 16px;
    flex-shrink: 0;
}

/* 调整下拉框文本对齐方式 */
#product-test-query-content .form-select {
    text-align: left;  /* 设置文本左对齐 */
    padding-left: 14px;  /* 确保文本有适当的左边距 */
}

/* 特别针对测试结果下拉框 */
#product-test-test-result {
    text-align-last: left;  /* 确保下拉框选项也是左对齐 */
}

/* 在文件末尾添加响应式布局样式 */

/* 大屏幕布局 (1200px以上) */
@media screen and (min-width: 1201px) {
    #product-test-query-content .query-form {
        flex-wrap: nowrap;
        gap: 16px;
    }
}

/* 中等屏幕布局 (992px - 1200px) */
@media screen and (max-width: 1200px) {
    #product-test-query-content .query-form {
        flex-wrap: nowrap;
        gap: 12px;
    }
    
    #product-test-query-content .form-label {
        font-size: 13px;
    }
    
    #product-test-query-content .form-input,
    #product-test-query-content .form-select {
        padding: 8px 12px;
        font-size: 13px;
    }
}

/* 平板布局 (768px - 991px) */
@media screen and (max-width: 991px) {
    #product-test-query-content .query-form {
        flex-wrap: wrap;
        gap: 12px;
    }
    
    #product-test-query-content .form-item {
        flex: 1 1 calc(33.333% - 12px);
        min-width: 200px;
    }
    
    #product-test-query-content .query-buttons {
        margin-left: 0;
        padding-top: 8px;
        width: 100%;
        justify-content: flex-end;
    }
}

/* 手机布局 (768px以下) */
@media screen and (max-width: 767px) {
    #product-test-query-content .query-section {
        padding: 16px;
    }
    
    #product-test-query-content .query-form {
        gap: 8px;
    }
    
    #product-test-query-content .form-item {
        flex: 1 1 100%;
    }
    
    #product-test-query-content .form-item:nth-child(3),
    #product-test-query-content .form-item:nth-child(4),
    #product-test-query-content .form-item:nth-child(5) {
        flex: 1 1 100%;
    }
    
    #product-test-query-content .query-buttons {
        flex-direction: row;
        justify-content: space-between;
        width: 100%;
    }
    
    #product-test-query-content .btn {
        flex: 1;
        margin: 0 4px;
    }
}

/* 确保表格在小屏幕上可以横向滚动 */
@media screen and (max-width: 1200px) {
    #product-test-query-content .table-section {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    #product-test-query-content .data-table {
        min-width: 900px; /* 确保表格内容不会被压缩 */
    }
}

/* 优化分页控件在小屏幕上的显示 */
@media screen and (max-width: 767px) {
    #product-test-query-content .pagination-section {
        flex-direction: column;
        gap: 16px;
    }
    
    #product-test-query-content .pagination-controls {
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
        gap: 8px;
    }
    
    #product-test-query-content .page-size {
        width: 100%;
        justify-content: center;
    }
}

/* 优化模态框在小屏幕上的显示 */
@media screen and (max-width: 767px) {
    .product-test-query__modal-content {
        width: 95%;
        margin: 10px;
        max-height: 90vh;
    }
    
    .product-test-query__modal-body {
        padding: 16px;
    }
    
    .product-test-query__info-grid {
        grid-template-columns: 1fr;
    }
}

/* 添加打印媒体查询 */
@media print {
    #product-test-query-content {
        padding: 0;
    }
    
    #product-test-query-content .query-section,
    #product-test-query-content .toolbar-section {
        display: none;
    }
    
    #product-test-query-content .table-section {
        overflow: visible;
    }
    
    #product-test-query-content .pagination-section {
        display: none;
    }
}

/* 在文件末尾添加总条数样式 */
#product-test-query-content .total-count {
    margin-left: 16px;
    color: hsl(var(--muted-foreground));
    font-size: 14px;
}

/* 调整响应式布局中的总条数显示 */
@media screen and (max-width: 767px) {
    #product-test-query-content .total-count {
        margin-left: 8px;
    }
}

/* 在文件末尾添加或更新 Toast 样式 */

/* Toast 容器样式 */
.product-test-query__toast-container {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 9999;
    pointer-events: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
}

.product-test-query__toast {
    background: white;
    padding: 12px 24px;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    opacity: 0;
    transition: all 0.3s ease;
    pointer-events: none;
}

.product-test-query__toast.show {
    opacity: 1;
    transform: translateY(0);
}

/* Toast 内容样式 */
.product-test-query__toast-content {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    line-height: 1.5;
}

/* Toast 图标样式 */
.product-test-query__toast i {
    font-size: 16px;
}

/* Toast 类型样式 */
.product-test-query__toast--success {
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
}

.product-test-query__toast--success i {
    color: #52c41a;
}

.product-test-query__toast--error {
    background-color: #fff1f0;
    border: 1px solid #ffccc7;
}

.product-test-query__toast--error i {
    color: #ff4d4f;
}

.product-test-query__toast--warning {
    background-color: #fffbe6;
    border: 1px solid #ffe58f;
}

.product-test-query__toast--warning i {
    color: #faad14;
}

.product-test-query__toast--info {
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;
}

.product-test-query__toast--info i {
    color: #1890ff;
}

/* 动画效果 */
@keyframes productTestToastSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes productTestToastSlideOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-20px);
    }
}

.product-test-query__toast {
    animation: productTestToastSlideIn 0.3s ease forwards;
}

.product-test-query__toast.hide {
    animation: productTestToastSlideOut 0.3s ease forwards;
}

/* 添加表格布局控制 */
#product-test-query-content .table-section {
    overflow-x: auto;
    width: 100%;
}

#product-test-query-content .data-table {
    table-layout: fixed;
    width: max-content;
    min-width: 100%;
    white-space: nowrap;
}

/* 定义列宽类 */
#product-test-query-content .table-cell-fixed {
    width: 50px !important;
    min-width: 50px !important;
}

#product-test-query-content .table-cell-wide {
    width: 180px !important;
    min-width: 180px !important;
}

#product-test-query-content .table-cell-normal {
    width: 100px !important;
    min-width: 100px !important;
}

#product-test-query-content .table-cell-medium {
    width: 120px !important;
    min-width: 120px !important;
}

#product-test-query-content .table-cell-action {
    width: 120px !important;
    min-width: 120px !important;
}

/* 单元格内容溢出处理 */
#product-test-query-content .table-cell-center {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 12px 8px !important;
}

/* 特定类型的单元格样式 */
#product-test-query-content .text-code {
    font-family: monospace;
}

#product-test-query-content .text-status {
    text-align: center;
}

#product-test-query-content .text-number {
    text-align: right;
}

#product-test-query-content .text-name {
    text-align: left;
}

#product-test-query-content .text-date {
    text-align: center;
}

/* 确保表格容器始终显示水平滚动条 */
#product-test-query-content .table-section::-webkit-scrollbar {
    height: 8px;
    background-color: #f5f5f5;
}

#product-test-query-content .table-section::-webkit-scrollbar-thumb {
    background-color: #ddd;
    border-radius: 4px;
}

#product-test-query-content .table-section::-webkit-scrollbar-thumb:hover {
    background-color: #ccc;
}

/* 列设置模态框样式调整 */
.product-test-column-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    overflow: auto;
}

.product-test-column-modal__content {
    position: relative;
    top: 5%; /* 从顶部 5% 开始显示 */
    margin: 0 auto;
    width: 90%;
    max-width: 600px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    max-height: 80vh;
    overflow-y: auto;
}

/* 优化滚动条样式 */
.product-test-column-modal__content::-webkit-scrollbar {
    width: 6px;
}

.product-test-column-modal__content::-webkit-scrollbar-thumb {
    background-color: #ccc;
    border-radius: 3px;
}

.product-test-column-modal__content::-webkit-scrollbar-track {
    background-color: #f5f5f5;
}

/* 确保模态框内容布局合理 */
.product-test-column-modal .modal-body {
    max-height: calc(80vh - 120px);
    overflow-y: auto;
    padding: 16px 24px;
}

/* 响应式调整 */
@media screen and (max-height: 768px) {
    .product-test-column-modal__content {
        top: 5%;
        max-height: 90vh;
    }
}

/* 排序样式 */
#product-test-query-content .sortable {
    cursor: pointer;
    position: relative;
    user-select: none;
}

#product-test-query-content .sortable:hover {
    background-color: #f5f5f5;
}

#product-test-query-content .sortable i {
    margin-left: 5px;
    font-size: 12px;
    color: #999;
}

#product-test-query-content .sortable:hover i {
    color: #666;
}

#product-test-query-content .sortable i.fa-sort-up,
#product-test-query-content .sortable i.fa-sort-down {
    color: #1890ff;
}

/* 使表格单元格内容超出时显示省略号 */
#product-test-query-content .data-table td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
}

/* 鼠标悬停时显示提示 */
#product-test-query-content .data-table td:hover::after {
    content: attr(title);
    position: absolute;
    left: 0;
    top: 100%;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    white-space: pre-wrap;
    max-width: 300px;
    word-break: break-all;
    font-size: 12px;
    display: none; /* 默认不显示，需要通过JS控制 */
}

#product-test-query-content .data-table td.dblclick-detail {
    cursor: pointer;
    transition: background-color 0.2s;
}
#product-test-query-content .data-table td.dblclick-detail:hover {
    background-color: #a4c3f1;
}

/* 新增：AUTO标签样式 */
.auto-tag {
    background-color: #e6f7ff !important;
    border-color: #91d5ff !important;
    color: #1890ff !important;
    font-size: 10px !important;
    font-weight: bold !important;
    padding: 1px 6px !important;
    border-radius: 3px !important;
    border: 1px solid #91d5ff !important;
    display: inline-flex !important;
    align-items: center !important;
    white-space: nowrap !important;
}

.auto-tag i {
    margin-right: 2px !important;
    font-size: 8px !important;
}

/* AUTO统计容器样式 */
.auto-stats-container {
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
    margin-left: 16px !important;
    font-size: 14px !important;
}

.auto-stats-container span {
    display: inline-flex !important;
    align-items: center !important;
    white-space: nowrap !important;
}

/* 测试项目容器优化 */
.product-test-query__test-item {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 8px 0 !important;
    border-bottom: 1px solid #f0f0f0 !important;
}

.product-test-query__test-item:last-child {
    border-bottom: none !important;
}

/* 模态框标题区域优化 */
.product-test-query__modal-title {
    display: flex !important;
    flex-direction: column !important;
    gap: 8px !important;
}

/* 测试结果卡片标题栏优化 */
.product-test-query__card-header {
    position: relative;
}

.product-test-query__test-tools {
    flex-shrink: 0;
}

.product-test-query__test-tools .m-area-log-toggle-btn {
    transition: all 0.2s ease;
    font-weight: 500;
}

.product-test-query__test-tools .m-area-log-toggle-btn:hover {
    background: linear-gradient(135deg, #40a9ff 0%, #69c0ff 100%) !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(24,144,255,0.3) !important;
}

.product-test-query__test-tools .m-area-log-toggle-btn:disabled {
    background: #f5f5f5 !important;
    color: #bfbfbf !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: none !important;
}

/* 响应式设计：小屏幕上auto标签样式调整 */
@media screen and (max-width: 767px) {
    .auto-stats-container {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 4px !important;
        margin-left: 0 !important;
        margin-top: 8px !important;
    }
    
    .auto-tag {
        font-size: 9px !important;
        padding: 1px 4px !important;
    }
}