// 使用IIFE创建模块作用域
(function() {
    // 检验记录查询状态管理
    window.inspectionRecordState = {
        currentPage: 1,
        pageSize: 10,
        pageSizeOptions: [10, 20, 50, 100],
        totalCount: 0,
        totalPages: 0,
        queryType: 'orderNo', // 默认查询类型：工单号
        queryValue: '', // 查询值
        sortField: '', 
        sortOrder: 'asc',
        isInitialSearch: true,
        selectedRecords: new Set(), // 添加用于跟踪选中记录的集合
        allSelected: false // 添加用于跟踪是否全选的标志
    };

    function initInspectionRecordPage() {
        // 加载SweetAlert2库
        if (!document.getElementById('sweetalert2-script')) {
            const script = document.createElement('script');
            script.id = 'sweetalert2-script';
            script.src = '/static/lib/sweetalert2/sweetalert2.min.js';
            document.head.appendChild(script);
            
            // 如果需要CSS
            if (!document.getElementById('sweetalert2-css')) {
                const link = document.createElement('link');
                link.id = 'sweetalert2-css';
                link.rel = 'stylesheet';
                link.href = '/static/lib/sweetalert2/sweetalert2.min.css';
                document.head.appendChild(link);
            }
        }
        
        const content = document.getElementById('content');
        content.innerHTML = createInspectionRecordHTML();
        
        // 初始化事件监听
        initEventListeners();
        updatePagination(); // 初始加载时也更新一次分页，确保显示
    }

    function createInspectionRecordHTML() {
        return `
            <section class="inspection-record">
                <header>
                    <h1 class="inspection-record__title">查询类型</h1>
                </header>
                
                <div class="inspection-record__header">
                    <div class="inspection-record__type">
                        <div class="inspection-record__type-options">
                            <label class="inspection-record__type-option">
                                <input type="radio" name="query-type" value="orderNo" class="inspection-record__type-radio" checked>
                                <span class="inspection-record__type-text">工单号</span>
                            </label>
                            <label class="inspection-record__type-option">
                                <input type="radio" name="query-type" value="serialNumber" class="inspection-record__type-radio">
                                <span class="inspection-record__type-text">SN号</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="inspection-record__filter">
                        <button type="button" class="inspection-record__filter-btn">
                            <span class="inspection-record__filter-text">高级查询</span>
                            <i class="fas fa-chevron-down inspection-record__filter-icon"></i>
                        </button>
                    </div>
                </div>
                
                <div class="inspection-record__advanced-filter" style="display: none;">
                    <div class="inspection-record__date-range">
                        <div class="inspection-record__date-field">
                            <label class="inspection-record__date-label">开始日期</label>
                            <input type="date" class="inspection-record__date-input" id="start-date">
                        </div>
                        <div class="inspection-record__date-field">
                            <label class="inspection-record__date-label">结束日期</label>
                            <input type="date" class="inspection-record__date-input" id="end-date">
                        </div>
                    </div>
                </div>
                
                <form class="inspection-record__form">
                    <div class="inspection-record__form-group">
                        <input type="text" id="query-input" class="inspection-record__input" placeholder="请输入工单号">
                    </div>
                    
                    <div class="inspection-record__buttons">
                        <button type="button" id="search-btn" class="inspection-record__btn inspection-record__btn--search">
                            <i class="fas fa-search"></i> 查询
                        </button>
                        <button type="button" id="reset-btn" class="inspection-record__btn inspection-record__btn--reset">
                            <i class="fas fa-redo"></i> 重置
                        </button>
                        <button type="button" id="export-inspection-btn" class="inspection-record__btn inspection-record__btn--export" style="display: none;">
                            <i class="fas fa-file-export"></i> 导出数据
                        </button>
                    </div>
                </form>
                
                <div class="inspection-record__results">
                    <div class="inspection-record__table-container">
                        <table class="inspection-record__table">
                            <thead>
                                <tr>
                                    <th width="5%">
                                        <input type="checkbox" id="select-all-checkbox-inspection" class="inspection-record__checkbox">
                                    </th>
                                    <th width="7%">序号</th>
                                    <th width="25%" class="sortable" onclick="window.sortBy('order_no')">
                                        工单号 <i class="fas fa-sort"></i>
                                    </th>
                                    <th width="15%" class="sortable" onclick="window.sortBy('serial_number')">
                                        SN号 <i class="fas fa-sort"></i>
                                    </th>
                                    <th width="15%" class="sortable" onclick="window.sortBy('product_type')">
                                        产品类型 <i class="fas fa-sort"></i>
                                    </th>
                                    <th width="13%" class="sortable" onclick="window.sortBy('status')">
                                        工单状态 <i class="fas fa-sort"></i>
                                    </th>
                                    <th width="12%" class="sortable" onclick="window.sortBy('created_at')">
                                        创建时间 <i class="fas fa-sort"></i>
                                    </th>
                                    <th width="8%">操作</th>
                                </tr>
                            </thead>
                            <tbody id="results-body">
                                <!-- 结果将通过JavaScript动态添加 -->
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="inspection-record__pagination">
                        <div class="inspection-record__pagination-info">
                            <span>每页</span>
                            <select id="page-size" class="inspection-record__page-size">
                                <option value="10">10</option>
                                <option value="20">20</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                            <span>条</span>
                            <span class="inspection-record__total">共 <span id="total-count">0</span> 条</span>
                        </div>
                        <div class="inspection-record__pagination-controls" id="pagination-controls">
                            <!-- 分页控件将通过JavaScript动态添加 -->
                        </div>
                    </div>
                </div>
                
                <!-- 检验记录详情模态框 -->
                <div id="detail-modal" class="inspection-record__detail-modal">
                    <div class="inspection-record__detail-content">
                        <button type="button" class="inspection-record__detail-close" onclick="window.closeDetailModal()">
                            <i class="fas fa-times"></i>
                        </button>
                        <div class="inspection-record__detail-header">
                            <h3 class="inspection-record__detail-title">检验记录详情</h3>
                            <div class="inspection-record__detail-subtitle">
                                工单号: <span id="detail-order-no"></span> | 
                                SN号: <span id="detail-serial-number"></span> |
                                产品类型: <span id="detail-product-type"></span>
                            </div>
                        </div>
                        <div class="inspection-record__stages" id="inspection-stages">
                            <!-- 检验阶段将通过JavaScript动态添加 -->
                        </div>
                    </div>
                </div>
            </section>
        `;
    }

    function initEventListeners() {
        // 查询类型切换
        const queryTypeRadios = document.querySelectorAll('input[name="query-type"]');
        const queryInput = document.getElementById('query-input');
        
        queryTypeRadios.forEach(radio => {
            radio.addEventListener('change', (e) => {
                const queryType = e.target.value;
                window.inspectionRecordState.queryType = queryType;
                
                // 根据查询类型更新输入框占位符
                switch(queryType) {
                    case 'serialNumber':
                        queryInput.placeholder = '请输入SN号';
                        break;
                    case 'orderNo':
                        queryInput.placeholder = '请输入工单号';
                        break;
                }
            });
        });
        
        // 高级查询按钮点击事件
        const filterBtn = document.querySelector('.inspection-record__filter-btn');
        const advancedFilter = document.querySelector('.inspection-record__advanced-filter');
        
        filterBtn.addEventListener('click', () => {
            const isVisible = advancedFilter.style.display !== 'none';
            advancedFilter.style.display = isVisible ? 'none' : 'block';
            filterBtn.classList.toggle('active');
        });
        
        // 日期选择器初始化
        const startDate = document.getElementById('start-date');
        const endDate = document.getElementById('end-date');
        
        // 清空默认日期值
        startDate.value = '';
        endDate.value = '';
        
        // 确保结束日期不能早于开始日期
        startDate.addEventListener('change', () => {
            if (endDate.value && startDate.value > endDate.value) {
                endDate.value = startDate.value;
            }
            endDate.min = startDate.value;
        });
        
        // 确保开始日期不能晚于结束日期
        endDate.addEventListener('change', () => {
            if (startDate.value && endDate.value < startDate.value) {
                startDate.value = endDate.value;
            }
            startDate.max = endDate.value;
        });
        
        // 查询按钮点击事件
        const searchBtn = document.getElementById('search-btn');
        searchBtn.addEventListener('click', () => {
            const queryValue = queryInput.value.trim();
            const startDate = document.getElementById('start-date').value;
            const endDate = document.getElementById('end-date').value;
            
            // 检查是否至少有一个查询条件（查询值或日期范围）
            if (!queryValue && !startDate && !endDate) {
                Swal.fire({
                    icon: 'warning',
                    title: '请输入至少一个查询条件',
                    text: '可以输入查询内容或选择日期范围',
                    showConfirmButton: false,
                    timer: 2000
                });
                return;
            }
            
            window.inspectionRecordState.queryValue = queryValue;
            window.inspectionRecordState.currentPage = 1; // 重置为第一页
            window.inspectionRecordState.isInitialSearch = true; // 设置为初始查询
            searchInspectionRecords();
        });
        
        // 重置按钮点击事件
        const resetBtn = document.getElementById('reset-btn');
        resetBtn.addEventListener('click', () => {
            queryInput.value = '';
            window.inspectionRecordState.queryValue = '';
            
            // 重置查询类型为默认值
            const defaultRadio = document.querySelector('input[name="query-type"][value="orderNo"]');
            if(defaultRadio) defaultRadio.checked = true;
            window.inspectionRecordState.queryType = 'orderNo';
            queryInput.placeholder = '请输入工单号';
            
            // 清空日期输入
            if (document.getElementById('start-date')) {
                document.getElementById('start-date').value = '';
            }
            if (document.getElementById('end-date')) {
                document.getElementById('end-date').value = '';
            }
            
            // 清空结果
            document.getElementById('results-body').innerHTML = '';
            document.getElementById('total-count').textContent = '0';
            window.inspectionRecordState.totalCount = 0;
            window.inspectionRecordState.totalPages = 0;
            updatePagination(); // Ensure pagination is reset to initial state

            // 隐藏导出按钮并重置选中状态
            hideExportInspectionButton();
            window.inspectionRecordState.selectedRecords.clear();
            window.inspectionRecordState.allSelected = false;
            const selectAllCheckboxInspection = document.getElementById('select-all-checkbox-inspection');
            if(selectAllCheckboxInspection) selectAllCheckboxInspection.checked = false;
            updateExportInspectionButtonText(); 
        });
        
        // 每页显示数量变更事件
        const pageSizeSelect = document.getElementById('page-size');
        pageSizeSelect.addEventListener('change', (e) => {
            window.inspectionRecordState.pageSize = parseInt(e.target.value);
            window.inspectionRecordState.currentPage = 1; // 重置为第一页
            
            // 检查是否已经有查询结果（通过总记录数判断）
            if (window.inspectionRecordState.totalCount > 0) {
                searchInspectionRecords();
            }
        });
        
        // 回车键触发查询
        queryInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                searchBtn.click();
            }
        });

        // 添加全选复选框事件监听
        const selectAllCheckboxInspection = document.getElementById('select-all-checkbox-inspection');
        if (selectAllCheckboxInspection) {
            selectAllCheckboxInspection.addEventListener('change', handleSelectAllInspection);
        }
        
        // 导出按钮点击事件
        const exportInspectionBtn = document.getElementById('export-inspection-btn');
        if (exportInspectionBtn) {
            exportInspectionBtn.addEventListener('click', exportInspectionData); // 将创建 exportInspectionData 函数
        }
    }

    // 搜索检验记录
    function searchInspectionRecords() {
        const { queryType, queryValue, currentPage, pageSize, sortField, sortOrder, isInitialSearch } = window.inspectionRecordState;
        
        const advancedFilterVisible = document.querySelector('.inspection-record__advanced-filter').style.display !== 'none';
        const startDate = advancedFilterVisible ? document.getElementById('start-date').value : '';
        const endDate = advancedFilterVisible ? document.getElementById('end-date').value : '';
        
        showLoadingIndicator();
        
        const queryParams = new URLSearchParams({ type: queryType, value: queryValue, page: currentPage, pageSize: pageSize });
        if (sortField && sortField.trim() !== '') queryParams.append('sort_field', sortField);
        if (sortOrder) queryParams.append('sort_order', sortOrder);
        if (advancedFilterVisible) {
            if (startDate) queryParams.append('startDate', startDate);
            if (endDate) queryParams.append('endDate', endDate);
        }
        
        Logger.log('查询参数:', Object.fromEntries(queryParams));
        
        fetchInspectionRecords(queryParams.toString())
            .then(data => {
                if (data.success) {
                    renderResults(data.records, data.totalCount);
                    if (sortField) updateSortIcons(sortField);
                    
                    if (data.totalCount > 0) {
                        showExportInspectionButton();
                    } else {
                        hideExportInspectionButton();
                    }
                    
                    if (isInitialSearch) {
                        showSearchResultNotification(data.totalCount);
                        window.inspectionRecordState.isInitialSearch = false;
                    }
                } else {
                    showError(data.message || '查询失败');
                    showErrorNotification(data.message || '查询失败，请稍后重试');
                    hideExportInspectionButton(); // Ensure button is hidden on error
                }
            })
            .catch(error => {
                Logger.error('查询出错:', error);
                showError('查询出错，请稍后再试');
                showErrorNotification(error.message || '请稍后再试');
                hideExportInspectionButton(); // Ensure button is hidden on error
            })
            .finally(() => hideLoadingIndicator());
    }

    // 使用Promise封装API请求
    function fetchInspectionRecords(queryParams) {
        return new Promise((resolve, reject) => {
            fetch(`/api/inspection-records/search?${queryParams}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('网络响应不正常');
                    }
                    return response.json();
                })
                .then(data => resolve(data))
                .catch(error => reject(error));
        });
    }

    // 显示加载指示器
    function showLoadingIndicator() {
        const tableContainer = document.querySelector('.inspection-record__table-container');
        if (!document.querySelector('.table-loading-indicator')) {
            const loadingIndicator = document.createElement('div');
            loadingIndicator.className = 'table-loading-indicator';
            loadingIndicator.innerHTML = '<div class="loading-spinner"></div>';
            tableContainer.appendChild(loadingIndicator);
        }
    }

    // 隐藏加载指示器
    function hideLoadingIndicator() {
        const loadingIndicator = document.querySelector('.table-loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.remove();
        }
    }

    // 显示查询结果通知
    function showSearchResultNotification(totalCount) {
        if (totalCount > 0) {
            Swal.fire({
                icon: 'success',
                title: '查询成功',
                text: `共找到 ${totalCount} 条匹配记录`,
                position: 'center',
                showConfirmButton: false,
                timer: 2000
            });
        } else {
            Swal.fire({
                icon: 'info',
                title: '未找到记录',
                text: '没有找到匹配的记录，请尝试其他查询条件',
                position: 'center',
                showConfirmButton: false,
                timer: 2000
            });
        }
    }

    // 显示错误通知
    function showErrorNotification(message) {
        Swal.fire({
            icon: 'error',
            title: '查询出错',
            text: message,
            confirmButtonText: '确定'
        });
    }

    // 渲染查询结果
    function renderResults(records, totalCount) {
        const resultsBody = document.getElementById('results-body');
        const totalCountElement = document.getElementById('total-count');
        
        window.inspectionRecordState.selectedRecords.clear();
        window.inspectionRecordState.allSelected = false;
        const selectAllCheckbox = document.getElementById('select-all-checkbox-inspection');
        if (selectAllCheckbox) selectAllCheckbox.checked = false;
        
        window.inspectionRecordState.totalCount = totalCount;
        window.inspectionRecordState.totalPages = Math.ceil(totalCount / window.inspectionRecordState.pageSize);
        totalCountElement.textContent = totalCount;
        resultsBody.innerHTML = '';
        
        if (records.length === 0) {
            resultsBody.innerHTML = '<tr><td colspan="8" class="inspection-record__no-data">未找到匹配的记录</td></tr>'; // Colspan is 8 (checkbox + 序号 + 5 data cols + 操作)
            hideExportInspectionButton();
        } else {
            if(totalCount > 0) showExportInspectionButton(); else hideExportInspectionButton();
        }
        
        if (records.length === 0) { // Ensure pagination is also handled correctly
             updatePagination(); return;
        }

        const startIndex = (window.inspectionRecordState.currentPage - 1) * window.inspectionRecordState.pageSize + 1;
        records.forEach((record, index) => {
            const row = document.createElement('tr');
            const createdAt = new Date(record.created_at);
            const formattedDate = `${createdAt.getFullYear()}-${String(createdAt.getMonth() + 1).padStart(2, '0')}-${String(createdAt.getDate()).padStart(2, '0')}`;
            const { statusText, statusClass } = getStatusInfo(record.status);
            const recordId = record.id; // Assuming record.id is the unique identifier
            
            // 设置行的数据属性，用于详情查询
            if (record.detail_query_type) {
                row.setAttribute('data-detail-query-type', record.detail_query_type);
            }
            if (record.product_id_for_detail) {
                row.setAttribute('data-product-id-for-detail', record.product_id_for_detail);
            }
            
            row.innerHTML = `
                <td>
                    <input type="checkbox" class="inspection-record__checkbox row-checkbox-inspection" data-record-id="${recordId}">
                </td>
                <td>${startIndex + index}</td>
                <td>${record.order_no || '-'}</td>
                <td>${record.serial_number || '-'}</td>
                <td>${record.product_type || '-'}</td>
                <td><span class="inspection-record__status ${statusClass}">${statusText}</span></td>
                <td>${formattedDate}</td>
                <td>
                    <button type="button" class="inspection-record__action-btn" onclick="window.viewDetail('${recordId}')">
                        <i class="fas fa-eye"></i> 查看详情
                    </button>
                </td>
            `;
            resultsBody.appendChild(row);
        });
        
        document.querySelectorAll('.row-checkbox-inspection').forEach(checkbox => {
            checkbox.addEventListener('change', handleRowSelectInspection);
        });
        
        updatePagination();
        updateExportInspectionButtonText();
    }

    // 获取状态信息
    function getStatusInfo(status) {
        let statusText = '未知';
        let statusClass = '';
        
        switch(status) {
            case 'completed':
                statusText = '已完成';
                statusClass = 'inspection-record__status--completed';
                break;
            case 'in_progress':
                statusText = '进行中';
                statusClass = 'inspection-record__status--in-progress';
                break;
            case 'processing':
                statusText = '检验中';
                statusClass = 'inspection-record__status--in-progress';
                break;
            case 'pending':
                statusText = '待处理';
                statusClass = 'inspection-record__status--pending';
                break;
            default:
                statusText = status || '未知';
                break;
        }
        
        return { statusText, statusClass };
    }

    // 显示错误信息
    function showError(message) {
        const resultsBody = document.getElementById('results-body');
        resultsBody.innerHTML = `<tr><td colspan="8" class="inspection-record__no-data">${message}</td></tr>`; // Colspan 8
        
        document.getElementById('total-count').textContent = '0';
        window.inspectionRecordState.totalCount = 0;
        window.inspectionRecordState.totalPages = 0;
        updatePagination(); // Update pagination to show disabled state
        hideExportInspectionButton(); // Hide export button on error
    }

    // 更新分页控件
    function updatePagination() {
        const paginationControls = document.getElementById('pagination-controls');
        let { currentPage, totalPages } = window.inspectionRecordState; // Use inspectionRecordState

        // 如果 totalPages 无效或为0，我们仍然希望显示一个禁用的分页结构，默认为1页
        const displayTotalPages = Math.max(totalPages, 0); 
        const displayCurrentPage = currentPage;

        let paginationHTML = '';

        // 首页按钮
        paginationHTML += `
            <button class="inspection-record__pagination-btn ${displayCurrentPage === 1 || displayTotalPages === 0 ? 'disabled' : ''}" 
                    title="首页"
                    onclick="window.goToFirstPage()" ${displayCurrentPage === 1 || displayTotalPages === 0 ? 'disabled' : ''}>
                <i class="fas fa-angle-double-left"></i>
            </button>
        `;
        
        // 上一页按钮
        paginationHTML += `
            <button class="inspection-record__pagination-btn ${displayCurrentPage === 1 || displayTotalPages === 0 ? 'disabled' : ''}" 
                    title="上一页"
                    onclick="window.goToPrevPage()" ${displayCurrentPage === 1 || displayTotalPages === 0 ? 'disabled' : ''}>
                <i class="fas fa-angle-left"></i>
            </button>
        `;
        
        // 页码按钮
        if (displayTotalPages === 0) {
            paginationHTML += `
                <button class="inspection-record__pagination-btn active disabled">1</button>
            `;
        } else {
            let startPage = Math.max(1, displayCurrentPage - 2);
            let endPage = Math.min(displayTotalPages, startPage + 4);
    
            if (endPage - startPage < 4 && displayTotalPages > 4) {
                startPage = Math.max(1, endPage - 4);
            }
            if (displayTotalPages <= 4) {
                startPage = 1;
            }
    
            for (let i = startPage; i <= endPage; i++) {
                paginationHTML += `
                    <button class="inspection-record__pagination-btn ${i === displayCurrentPage ? 'active' : ''}" 
                            onclick="window.goToPage(${i})">
                        ${i}
                    </button>
                `;
            }
        }
        
        // 下一页按钮
        paginationHTML += `
            <button class="inspection-record__pagination-btn ${displayCurrentPage === displayTotalPages || displayTotalPages === 0 ? 'disabled' : ''}" 
                    title="下一页"
                    onclick="window.goToNextPage()" ${displayCurrentPage === displayTotalPages || displayTotalPages === 0 ? 'disabled' : ''}>
                <i class="fas fa-angle-right"></i>
            </button>
        `;
        
        // 末页按钮
        paginationHTML += `
            <button class="inspection-record__pagination-btn ${displayCurrentPage === displayTotalPages || displayTotalPages === 0 ? 'disabled' : ''}" 
                    title="末页"
                    onclick="window.goToLastPage()" ${displayCurrentPage === displayTotalPages || displayTotalPages === 0 ? 'disabled' : ''}>
                <i class="fas fa-angle-double-right"></i>
            </button>
        `;
        
        paginationControls.innerHTML = paginationHTML;
    }

    // 查看详情
    function viewDetail(recordId) {
        // 获取当前行的数据（从表格中）
        const row = document.querySelector(`.row-checkbox-inspection[data-record-id="${recordId}"]`).closest('tr');
        const detailQueryType = row.getAttribute('data-detail-query-type') || 'work_order';
        const productIdForDetail = row.getAttribute('data-product-id-for-detail');
        
        // 显示加载中提示
        Swal.fire({
            title: '加载中...',
            text: '正在获取检验记录详情',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });
        
        // 构建查询参数
        let apiUrl = `/api/inspection-records/${recordId}/detail?detail_type=${detailQueryType}`;
        if (detailQueryType === 'serial_number' && productIdForDetail) {
            apiUrl += `&product_id=${productIdForDetail}`;
        }
        
        // 发送API请求
        fetch(apiUrl)
            .then(response => {
                if (!response.ok) {
                    throw new Error('网络响应不正常');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // 渲染详情
                    renderDetail(data.record);
                    // 显示模态框
                    document.getElementById('detail-modal').style.display = 'block';
                    // 关闭加载提示
                    Swal.close();
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: '获取详情失败',
                        text: data.message || '请稍后重试',
                        confirmButtonText: '确定'
                    });
                }
            })
            .catch(error => {
                Logger.error('获取详情出错:', error);
                Swal.fire({
                    icon: 'error',
                    title: '获取详情出错',
                    text: error.message || '请稍后再试',
                    confirmButtonText: '确定'
                });
            });
    }

    // 渲染详情内容
    function renderDetail(record) {
        // 设置基本信息
        document.getElementById('detail-order-no').textContent = record.basic_info.order_no || '-';
        
        // 处理SN号显示 - 如果是SN查询则显示查询的SN，否则显示工单关联的第一个SN
        let snDisplay = '-';
        if (record.basic_info.serial_no_queried) {
            snDisplay = record.basic_info.serial_no_queried;
        }
        document.getElementById('detail-serial-number').textContent = snDisplay;
        document.getElementById('detail-product-type').textContent = record.basic_info.product_model || '-';

        // --- 添加/调整导出为图片按钮 ---
        const modalContent = document.querySelector('.inspection-record__detail-content'); // 父容器
        const closeButton = modalContent.querySelector('.inspection-record__detail-close'); // 关闭按钮
        let exportImageBtn = document.getElementById('export-modal-image-btn');

        if (!exportImageBtn && closeButton) { // 确保关闭按钮存在才添加
            exportImageBtn = document.createElement('button');
            exportImageBtn.id = 'export-modal-image-btn';
            exportImageBtn.type = 'button';
            exportImageBtn.className = 'inspection-record__btn inspection-record__btn--export-image';
            exportImageBtn.innerHTML = '<i class="fas fa-camera"></i> 导出图片';
            exportImageBtn.onclick = () => exportModalAsImage();
            // 将导出按钮插入到关闭按钮之前
            closeButton.parentNode.insertBefore(exportImageBtn, closeButton);
        } else if (exportImageBtn && closeButton) {
            // 如果按钮已存在（例如，重复打开模态框），确保它在关闭按钮之前
            closeButton.parentNode.insertBefore(exportImageBtn, closeButton);
        }
        // --- 结束添加/调整按钮 ---
        
        // 渲染检验阶段
        const stagesContainer = document.getElementById('inspection-stages');
        stagesContainer.innerHTML = '';
        
        // 定义阶段名称映射
        const stageNameMap = {
            'assembly': '组装前检验',
            'test': '测试前检验',
            'packaging': '包装前检验'
        };
        
        // 定义角色名称映射
        const roleNameMap = {
            'first': '首检',
            'self': '自检',
            'ipqc': 'IPQC'
        };
        
        // 定义阶段颜色
        const stageColorMap = {
            'assembly': 'blue',
            'test': 'orange',
            'packaging': 'green'
        };
        
        // 新增：定义阶段和角色的期望顺序
        const orderedStageKeys = ['assembly', 'test', 'packaging'];
        const orderedRoleKeys = ['first', 'self', 'ipqc'];
        
        // 渲染所有阶段 (修改迭代方式)
        for (const stageKey of orderedStageKeys) {
            if (!record.stages.hasOwnProperty(stageKey)) continue; // 如果数据中不存在此阶段，则跳过
            const stageData = record.stages[stageKey];

            const stageName = stageNameMap[stageKey] || stageKey;
            const stageColor = stageColorMap[stageKey] || 'gray';
            
            // 创建阶段容器
            const stageElement = document.createElement('div');
            stageElement.className = 'inspection-record__stage';
            stageElement.classList.add(`inspection-record__stage--type-${stageKey}`);
            
            // 创建阶段标题栏
            const stageHeader = document.createElement('div');
            stageHeader.className = 'inspection-record__stage-header';
            stageHeader.onclick = () => toggleStage(stageHeader);
            
            stageHeader.innerHTML = `
                <div class="inspection-record__stage-title">
                    <i class="fas fa-chevron-down inspection-record__stage-icon"></i>
                    ${stageName}
                </div>
            `;
            
            // 将标题栏添加到阶段容器
            stageElement.appendChild(stageHeader);
            
            // 创建阶段内容区
            const stageBody = document.createElement('div');
            stageBody.className = 'inspection-record__stage-body';
            
            // 为每个角色创建一个部分 (修改迭代方式)
            for (const roleKey of orderedRoleKeys) {
                if (!stageData.hasOwnProperty(roleKey)) continue; // 如果数据中不存在此角色，则跳过
                const roleData = stageData[roleKey];

                const roleName = roleNameMap[roleKey] || roleKey;
                
                // 创建角色区域
                const roleSection = document.createElement('div');
                roleSection.className = 'inspection-record__role-section';
                roleSection.style.marginBottom = '20px';
                roleSection.classList.add(`inspection-record__role-section--role-${roleKey}`);
                
                // 创建角色标题
                const roleTitle = document.createElement('h4');
                roleTitle.className = 'inspection-record__role-title';
                roleTitle.textContent = roleName;
                roleTitle.style.borderBottom = '1px solid var(--color-border)';
                roleTitle.style.paddingBottom = '8px';
                roleSection.appendChild(roleTitle);
                
                // 创建角色状态信息区域
                const roleInfoContainer = document.createElement('div');
                roleInfoContainer.className = 'inspection-record__role-info-container';

                const completedBySnValue = roleData.completed_by_sn || '-';
                const inspectorValue = roleData.inspector || '-';
                const inspectionTimeValue = roleData.inspection_time || '-';

                roleInfoContainer.innerHTML = `
                    <span class="inspection-record__info-item-inline">检验人: ${inspectorValue}</span>
                    <span class="inspection-record__info-item-inline">检验时间: ${inspectionTimeValue}</span>
                    <span class="inspection-record__info-item-inline">完成SN号: ${completedBySnValue}</span>
                `;
                roleSection.appendChild(roleInfoContainer);
                
                // 创建检验项目列表
                if (roleData.items && roleData.items.length > 0) {
                    const itemsContainer = document.createElement('div');
                    itemsContainer.className = 'inspection-record__items';
                    itemsContainer.style.marginTop = '16px';
                    
                    const itemsTitle = document.createElement('h5');
                    itemsTitle.className = 'inspection-record__items-title';
                    itemsTitle.textContent = '检验项目';
                    itemsTitle.style.marginBottom = '8px';
                    itemsContainer.appendChild(itemsTitle);
                    
                    // 如果是SN维度，添加提示
                    if (record.basic_info.serial_no_queried) {
                        const snNote = document.createElement('div');
                        snNote.className = 'inspection-record__sn-note';
                        snNote.textContent = `以下项目状态基于SN: ${record.basic_info.serial_no_queried}`;
                        snNote.style.fontSize = 'var(--font-size-xs)';
                        snNote.style.color = 'var(--color-text-secondary)';
                        snNote.style.marginBottom = '8px';
                        itemsContainer.appendChild(snNote);
                    }
                    
                    // 创建项目表格
                    const itemsTable = document.createElement('table');
                    itemsTable.className = 'inspection-record__items-table';
                    
                    // 创建表头
                    const tableHead = document.createElement('thead');
                    tableHead.innerHTML = `
                        <tr>
                            <th width="10%">序号</th>
                            <th width="70%">检验项目</th>
                            <th width="20%">状态</th>
                        </tr>
                    `;
                    itemsTable.appendChild(tableHead);
                    
                    // 创建表体
                    const tableBody = document.createElement('tbody');
                    roleData.items.forEach((item, index) => {
                        const row = document.createElement('tr');
                        const statusClass = item.passed ? 
                            'inspection-record__status--completed' : 
                            'inspection-record__status--pending';
                        const statusText = item.passed ? '通过' : '未通过';
                        const noteText = item.note ? ` ${item.note}` : '';
                        
                        row.innerHTML = `
                            <td>${index + 1}</td>
                            <td>${item.name}</td>
                            <td>
                                <span class="inspection-record__status ${statusClass}">
                                    ${statusText}${noteText}
                                </span>
                            </td>
                        `;
                        tableBody.appendChild(row);
                    });
                    
                    itemsTable.appendChild(tableBody);
                    itemsContainer.appendChild(itemsTable);
                    roleSection.appendChild(itemsContainer);
                }
                
                // 添加附件列表（如果有）
                const stageAttachments = record.attachments && record.attachments[stageKey] ? 
                    record.attachments[stageKey][roleKey] : null;
                
                if (stageAttachments && stageAttachments.length > 0) {
                    const attachmentsContainer = document.createElement('div');
                    attachmentsContainer.className = 'inspection-record__attachments';
                    attachmentsContainer.style.marginTop = '16px';
                    
                    const attachmentsTitle = document.createElement('h5');
                    attachmentsTitle.className = 'inspection-record__attachments-title';
                    attachmentsTitle.textContent = '附件';
                    attachmentsTitle.style.marginBottom = '8px';
                    attachmentsContainer.appendChild(attachmentsTitle);
                    
                    // 创建附件列表
                    const attachmentsList = document.createElement('div');
                    attachmentsList.className = 'inspection-record__attachment-list';
                    
                    stageAttachments.forEach(attachment => {
                        const fileType = getFileType(attachment.name);
                        const icon = getFileIcon(fileType);
                        const fileSizeKB = attachment.file_size ? (attachment.file_size / 1024).toFixed(2) + ' KB' : '-';
                        const uploadTime = attachment.upload_time || '-';
                        const uploadedBy = attachment.uploaded_by || '-';
                        
                        const attachmentItem = document.createElement('div');
                        attachmentItem.className = 'inspection-record__attachment-item';
                        attachmentItem.onclick = () => previewFile(attachment.url, attachment.name, fileType);
                        
                        attachmentItem.innerHTML = `
                            <i class="${icon} inspection-record__attachment-icon"></i>
                            <div class="inspection-record__attachment-details">
                                <span class="inspection-record__attachment-name">${attachment.name}</span>
                                <span class="inspection-record__attachment-meta">${fileSizeKB} | ${uploadTime} | 上传人: ${uploadedBy}</span>
                            </div>
                        `;
                        
                        attachmentsList.appendChild(attachmentItem);
                    });
                    
                    attachmentsContainer.appendChild(attachmentsList);
                    roleSection.appendChild(attachmentsContainer);
                }
                
                // 将角色区域添加到阶段内容
                stageBody.appendChild(roleSection);
            }
            
            // 将内容区添加到阶段容器
            stageElement.appendChild(stageBody);
            
            // 将阶段容器添加到主容器
            stagesContainer.appendChild(stageElement);
        }
        
        // 如果没有阶段数据
        if (stagesContainer.children.length === 0) {
            stagesContainer.innerHTML = '<div class="inspection-record__no-data">暂无检验阶段数据</div>';
        }
    }

    // 渲染检验项目
    function renderInspectionItems(items) {
        if (!items || items.length === 0) {
            return '<div class="inspection-record__no-data">暂无检验项目数据</div>';
        }
        
        let html = `
            <div class="inspection-record__items">
                <h4>检验项目</h4>
                <table class="inspection-record__items-table">
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>检验项目</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
        `;
        
        items.forEach((item, index) => {
            html += `
                <tr>
                    <td>${index + 1}</td>
                    <td>${item.name}</td>
                    <td>
                        <span class="inspection-record__status ${item.passed ? 'inspection-record__status--completed' : 'inspection-record__status--pending'}">
                            ${item.passed ? '通过' : '未通过'}
                        </span>
                    </td>
                </tr>
            `;
        });
        
        html += `
                    </tbody>
                </table>
            </div>
        `;
        
        return html;
    }

    // 渲染附件
    function renderAttachments(attachments) {
        if (!attachments || attachments.length === 0) {
            return '';
        }
        
        let html = `
            <div class="inspection-record__attachments">
                <h4 class="inspection-record__attachments-title">附件</h4>
                <div class="inspection-record__attachment-list">
        `;
        
        attachments.forEach(attachment => {
            const fileType = getFileType(attachment.name);
            const icon = getFileIcon(fileType);
            
            html += `
                <div class="inspection-record__attachment-item" onclick="window.previewFile('${attachment.url}', '${attachment.name}', '${fileType}')">
                    <i class="${icon} inspection-record__attachment-icon"></i>
                    <span class="inspection-record__attachment-name">${attachment.name}</span>
                </div>
            `;
        });
        
        html += `
                </div>
            </div>
        `;
        
        return html;
    }

    // 获取文件类型
    function getFileType(fileName) {
        const extension = fileName.split('.').pop().toLowerCase();
        
        if (['jpg', 'jpeg', 'png', 'gif'].includes(extension)) {
            return 'image';
        } else if (['pdf'].includes(extension)) {
            return 'pdf';
        } else if (['doc', 'docx'].includes(extension)) {
            return 'word';
        } else if (['xls', 'xlsx'].includes(extension)) {
            return 'excel';
        } else if (['txt'].includes(extension)) {
            return 'text';
        } else {
            return 'other';
        }
    }

    // 获取文件图标
    function getFileIcon(fileType) {
        switch(fileType) {
            case 'image':
                return 'fas fa-file-image';
            case 'pdf':
                return 'fas fa-file-pdf';
            case 'word':
                return 'fas fa-file-word';
            case 'excel':
                return 'fas fa-file-excel';
            case 'text':
                return 'fas fa-file-alt';
            default:
                return 'fas fa-file';
        }
    }

    // 格式化日期时间
    function formatDateTime(dateTimeStr) {
        if (!dateTimeStr) return '-';
        
        const date = new Date(dateTimeStr);
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
    }

    // 切换阶段展开/折叠
    function toggleStage(headerElement) {
        const stageElement = headerElement.closest('.inspection-record__stage');
        stageElement.classList.toggle('collapsed');
    }

    // 关闭详情模态框
    function closeDetailModal() {
        document.getElementById('detail-modal').style.display = 'none';
    }

    // 分页相关函数
    function goToPage(page) {
        window.inspectionRecordState.currentPage = page;
        window.inspectionRecordState.isInitialSearch = false; // 设置为非初始查询
        searchInspectionRecords();
    }

    function goToFirstPage() {
        window.inspectionRecordState.currentPage = 1;
        window.inspectionRecordState.isInitialSearch = false; // 设置为非初始查询
        searchInspectionRecords();
    }

    function goToPrevPage() {
        if (window.inspectionRecordState.currentPage > 1) {
            window.inspectionRecordState.currentPage--;
            window.inspectionRecordState.isInitialSearch = false; // 设置为非初始查询
            searchInspectionRecords();
        }
    }

    function goToNextPage() {
        if (window.inspectionRecordState.currentPage < window.inspectionRecordState.totalPages) {
            window.inspectionRecordState.currentPage++;
            window.inspectionRecordState.isInitialSearch = false; // 设置为非初始查询
            searchInspectionRecords();
        }
    }

    function goToLastPage() {
        window.inspectionRecordState.currentPage = window.inspectionRecordState.totalPages;
        window.inspectionRecordState.isInitialSearch = false; // 设置为非初始查询
        searchInspectionRecords();
    }

    // 添加排序功能
    function sortBy(field) {
        // 更新排序状态
        if (window.inspectionRecordState.sortField === field) {
            // 如果点击的是当前排序字段，切换排序顺序
            window.inspectionRecordState.sortOrder = window.inspectionRecordState.sortOrder === 'asc' ? 'desc' : 'asc';
        } else {
            // 如果是新的排序字段，设置为升序
            window.inspectionRecordState.sortField = field;
            window.inspectionRecordState.sortOrder = 'asc';
        }
        
        // 重置到第一页
        window.inspectionRecordState.currentPage = 1;
        
        // 设置为非初始查询
        window.inspectionRecordState.isInitialSearch = false;
        
        // 更新排序图标并重新加载数据
        updateSortIcons(field);
        searchInspectionRecords();
    }

    // 更新排序图标
    function updateSortIcons(activeField) {
        const headers = document.querySelectorAll('.inspection-record__table th.sortable');
        headers.forEach(header => {
            const icon = header.querySelector('i');
            if (icon) {
                const field = header.getAttribute('onclick').match(/sortBy\('([^']+)'\)/)[1];
                if (field === activeField) {
                    // 更新激活的排序图标
                    icon.className = window.inspectionRecordState.sortOrder === 'asc' 
                        ? 'fas fa-sort-up' 
                        : 'fas fa-sort-down';
                    header.classList.add('active');
                } else {
                    // 重置其他列的图标
                    icon.className = 'fas fa-sort';
                    header.classList.remove('active');
                }
            }
        });
    }

    // 预览文件
    function previewFile(url, fileName, fileType) {
        if (fileType === 'image') {
            // 图片预览
            Swal.fire({
                title: fileName,
                imageUrl: url,
                imageAlt: fileName,
                imageWidth: '100%',
                confirmButtonText: '关闭'
            });
        } else if (fileType === 'pdf') {
            // PDF预览
            Swal.fire({
                title: fileName,
                html: `<iframe src="${url}" width="100%" height="500px" style="border:none;"></iframe>`,
                width: '80%',
                confirmButtonText: '关闭'
            });
        } else {
            // 其他文件类型，提供下载链接
            Swal.fire({
                title: '文件下载',
                text: `文件 "${fileName}" 不支持在线预览，是否下载？`,
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: '下载',
                cancelButtonText: '取消'
            }).then((result) => {
                if (result.isConfirmed) {
                    // 创建下载链接
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = fileName;
                    a.style.display = 'none';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                }
            });
        }
    }

    // 将函数挂载到window对象
    window.searchInspectionRecords = searchInspectionRecords;
    window.goToPage = goToPage;
    window.goToFirstPage = goToFirstPage;
    window.goToPrevPage = goToPrevPage;
    window.goToNextPage = goToNextPage;
    window.goToLastPage = goToLastPage;
    window.sortBy = sortBy;
    window.viewDetail = viewDetail;
    window.toggleStage = toggleStage;
    window.closeDetailModal = closeDetailModal;
    window.previewFile = previewFile;

    // ========= 新增导出相关辅助函数 =========
    // 显示导出按钮
    function showExportInspectionButton() {
        const exportBtn = document.getElementById('export-inspection-btn');
        if (exportBtn) {
            exportBtn.style.display = 'inline-flex';
        }
    }

    // 隐藏导出按钮
    function hideExportInspectionButton() {
        const exportBtn = document.getElementById('export-inspection-btn');
        if (exportBtn) {
            exportBtn.style.display = 'none';
        }
    }

    // 更新导出按钮文本
    function updateExportInspectionButtonText() {
        const exportBtn = document.getElementById('export-inspection-btn');
        if (exportBtn) {
            const selectedCount = window.inspectionRecordState.selectedRecords.size;
            if (selectedCount > 0) {
                exportBtn.innerHTML = `<i class="fas fa-file-export"></i> 导出选中(${selectedCount})`;
            } else {
                exportBtn.innerHTML = `<i class="fas fa-file-export"></i> 导出数据`;
            }
        }
    }

    // 处理全选/取消全选
    function handleSelectAllInspection(e) {
        const isChecked = e.target.checked;
        window.inspectionRecordState.allSelected = isChecked;
        
        const checkboxes = document.querySelectorAll('.row-checkbox-inspection'); // 注意类名
        checkboxes.forEach(checkbox => {
            checkbox.checked = isChecked;
            const recordId = checkbox.getAttribute('data-record-id');
            if (isChecked) {
                window.inspectionRecordState.selectedRecords.add(recordId);
            } else {
                window.inspectionRecordState.selectedRecords.delete(recordId);
            }
        });
        updateExportInspectionButtonText();
    }

    // 处理单行选择
    function handleRowSelectInspection(e) {
        const checkbox = e.target;
        const recordId = checkbox.getAttribute('data-record-id');
        
        if (checkbox.checked) {
            window.inspectionRecordState.selectedRecords.add(recordId);
        } else {
            window.inspectionRecordState.selectedRecords.delete(recordId);
            const selectAllCheckbox = document.getElementById('select-all-checkbox-inspection');
            if(selectAllCheckbox) selectAllCheckbox.checked = false;
            window.inspectionRecordState.allSelected = false;
        }
        
        const allCheckboxes = document.querySelectorAll('.row-checkbox-inspection');
        const allChecked = Array.from(allCheckboxes).every(cb => cb.checked);
        
        if (allChecked && allCheckboxes.length > 0) {
            const selectAllCheckbox = document.getElementById('select-all-checkbox-inspection');
            if(selectAllCheckbox) selectAllCheckbox.checked = true;
            window.inspectionRecordState.allSelected = true;
        }
        updateExportInspectionButtonText();
    }

    // 导出数据功能
    function exportInspectionData() {
        const { totalCount, selectedRecords, queryType, queryValue, sortField, sortOrder } = window.inspectionRecordState;
        
        if (totalCount <= 0 && selectedRecords.size === 0) {
            Swal.fire({
                icon: 'warning',
                title: '无数据可导出',
                text: '请先查询数据或选择要导出的记录',
                confirmButtonText: '确定'
            });
            return;
        }
        
        Swal.fire({
            title: '正在准备导出...',
            text: '请稍候',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });
        
        const queryParams = new URLSearchParams({
            type: queryType,
            value: queryValue,
            export: 'true' // 标志这是导出请求
        });
        
        if (sortField && sortField.trim() !== '') {
            queryParams.append('sort_field', sortField);
            queryParams.append('sort_order', sortOrder);
        }
        
        const advancedFilterVisible = document.querySelector('.inspection-record__advanced-filter').style.display !== 'none';
        if (advancedFilterVisible) {
            const startDate = document.getElementById('start-date').value;
            const endDate = document.getElementById('end-date').value;
            if (startDate) queryParams.append('startDate', startDate);
            if (endDate) queryParams.append('endDate', endDate);
        }
        
        if (selectedRecords.size > 0) {
            const selectedIds = Array.from(selectedRecords);
            queryParams.append('selected_records', JSON.stringify(selectedIds));
        }
        
        fetch(`/api/inspection-records/export?${queryParams.toString()}`)
            .then(response => {
                if (!response.ok) {
                    return response.json().then(err => {
                        throw new Error(err.message || '导出失败，服务器响应不正确');
                    }).catch(() => {
                        throw new Error(`导出失败，HTTP状态: ${response.status}`);
                    });
                }
                const contentType = response.headers.get("content-type");
                if (!contentType || !contentType.includes("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")) {
                    return response.json().then(errData => {
                        throw new Error(errData.message || "服务器未返回有效的Excel文件");
                    }).catch(() => {
                        throw new Error("服务器响应类型不正确，期望Excel文件");
                    });
                }
                return response.blob();
            })
            .then(blob => {
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                
                const date = new Date();
                const timestamp = `${date.getFullYear()}${String(date.getMonth() + 1).padStart(2, '0')}${String(date.getDate()).padStart(2, '0')}` +
                                  `${String(date.getHours()).padStart(2, '0')}${String(date.getMinutes()).padStart(2, '0')}`;
                
                const filenameSuffix = selectedRecords.size > 0 ? `_已选${selectedRecords.size}条` : '';
                a.download = `质检记录_${timestamp}${filenameSuffix}.xlsx`; // Filename for inspection records
                
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                
                Swal.fire({
                    icon: 'success',
                    title: '导出成功',
                    text: selectedRecords.size > 0 ? `已成功导出 ${selectedRecords.size} 条选中数据` : '数据已成功导出',
                    showConfirmButton: false,
                    timer: 2000
                });
            })
            .catch(error => {
                Logger.error('导出错误:', error);
                Swal.fire({
                    icon: 'error',
                    title: '导出失败',
                    text: error.message || '请稍后再试，或联系管理员',
                    confirmButtonText: '确定'
                });
            });
    }

    // --- 新增：导出模态框为图片函数 ---
    function exportModalAsImage() {
        const modalContent = document.getElementById('detail-modal'); // 我们要捕捉整个模态框，或者只是 detail-content
        const modalActualContent = document.querySelector('.inspection-record__detail-content');

        if (!modalActualContent) {
            Swal.fire('错误', '无法找到模态框内容进行导出。', 'error');
            return;
        }

        if (typeof html2canvas === 'undefined') {
            Swal.fire({
                icon: 'error',
                title: '导出功能缺失',
                text: 'html2canvas库未加载，无法导出图片。请检查控制台或联系管理员。',
                confirmButtonText: '确定'
            });
            Logger.error('html2canvas is not defined. Please ensure the library is loaded.');
            return;
        }

        Swal.fire({
            title: '正在生成图片...',
            text: '请稍候',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // 确保滚动到顶部，以捕捉完整内容（如果html2canvas默认只捕捉可见部分）
        // 对于html2canvas 1.x.x 通常会尝试捕捉整个元素包括滚动部分
        // const currentScrollY = modalActualContent.scrollTop;
        // modalActualContent.scrollTop = 0;

        html2canvas(modalActualContent, {
            scale: 2, // 提高图片分辨率
            useCORS: true, // 如果有跨域图片，需要这个
            logging: true, // 开启日志，方便调试
            onclone: (clonedDoc) => {
                // 可以在克隆的文档上做一些临时的修改，比如移除不必要的元素
                const clonedModalContent = clonedDoc.querySelector('.inspection-record__detail-content');
                if (clonedModalContent) {
                    // 确保克隆体在截图时是完全展开的，而不是依赖父容器的max-height和overflow
                    clonedModalContent.style.maxHeight = 'none';
                    clonedModalContent.style.overflow = 'visible';
                }
                // 移除导出按钮本身，避免出现在截图中
                const clonedExportBtn = clonedDoc.getElementById('export-modal-image-btn');
                if (clonedExportBtn) {
                    clonedExportBtn.style.display = 'none';
                }
            }
        }).then(canvas => {
            // modalActualContent.scrollTop = currentScrollY; // 恢复滚动位置
            const imageDataURL = canvas.toDataURL('image/png');
            const downloadLink = document.createElement('a');
            
            const orderNo = document.getElementById('detail-order-no').textContent || 'details';
            const timestamp = new Date().toISOString().slice(0, 19).replace(/[-:T]/g, '');
            downloadLink.download = `检验记录详情_${orderNo}_${timestamp}.png`;
            downloadLink.href = imageDataURL;
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);
            Swal.close();
            Swal.fire('成功', '图片已开始下载。', 'success');
        }).catch(err => {
            // modalActualContent.scrollTop = currentScrollY; // 恢复滚动位置
            Logger.error('导出图片失败:', err);
            Swal.fire('导出失败', `生成图片时发生错误: ${err.message || err}`, 'error');
        });
    }
    // --- 结束新增函数 ---

    // ========= END 新增导出相关辅助函数 =========

    // 初始化
    window.initInspectionRecordPage = initInspectionRecordPage;
})();

