#!/usr/bin/env python3
"""
测试SN号重复提交检测功能
"""

import requests
import json

# 测试配置
BASE_URL = "http://localhost:5000"
TEST_WORK_ORDER_ID = "TEST10086"  # 替换为实际的工单ID
TEST_SERIAL_NO = "GL200RTD400000000A1"  # 测试用的SN号
TEST_STAGE = "assembly"   # 组装前阶段
TEST_ROLE = "first"       # 首检角色

def test_check_serial_number():
    """测试检查SN号接口"""
    print("=== 测试SN号检查接口 ===")
    
    # 构建请求URL
    url = f"{BASE_URL}/api/quality-inspection/check-serial-number"
    params = {
        'work_order_id': TEST_WORK_ORDER_ID,
        'serial_no': TEST_SERIAL_NO,
        'stage': TEST_STAGE,
        'inspector_role': TEST_ROLE
    }
    
    try:
        response = requests.get(url, params=params)
        print(f"请求URL: {response.url}")
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("响应数据:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # 检查关键字段
            if data.get('success'):
                print("\n=== 检查结果分析 ===")
                print(f"SN号是否存在: {data.get('exists', False)}")
                
                if data.get('exists'):
                    print(f"产品ID: {data.get('product_id')}")
                    print(f"是否返工产品: {data.get('is_rework', False)}")
                    
                    # 检查提交状态
                    already_submitted = data.get('already_submitted')
                    if already_submitted:
                        print(f"已提交类型: {already_submitted}")
                        print(f"提交人: {data.get('submitted_by', '未知')}")
                        print(f"提交时间: {data.get('submitted_at', '未知')}")
                    else:
                        print("该SN在当前阶段和角色下尚未提交")
                else:
                    print("SN号不存在于当前工单")
                    if data.get('exists_in_other_work_order'):
                        print("但存在于其他工单")
            else:
                print(f"检查失败: {data.get('message', '未知错误')}")
        else:
            print(f"请求失败: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")

def test_different_scenarios():
    """测试不同场景"""
    print("\n=== 测试不同场景 ===")
    
    scenarios = [
        {
            'name': '组装前阶段-首检',
            'stage': 'assembly',
            'role': 'first'
        },
        {
            'name': '组装前阶段-自检',
            'stage': 'assembly', 
            'role': 'self'
        },
        {
            'name': '测试前阶段-首检',
            'stage': 'test',
            'role': 'first'
        }
    ]
    
    for scenario in scenarios:
        print(f"\n--- {scenario['name']} ---")
        url = f"{BASE_URL}/api/quality-inspection/check-serial-number"
        params = {
            'work_order_id': TEST_WORK_ORDER_ID,
            'serial_no': TEST_SERIAL_NO,
            'stage': scenario['stage'],
            'inspector_role': scenario['role']
        }
        
        try:
            response = requests.get(url, params=params)
            if response.status_code == 200:
                data = response.json()
                if data.get('success') and data.get('exists'):
                    already_submitted = data.get('already_submitted')
                    if already_submitted:
                        print(f"  ✓ 已提交 ({already_submitted})")
                    else:
                        print(f"  ○ 未提交")
                else:
                    print(f"  × SN不存在或检查失败")
            else:
                print(f"  × 请求失败 ({response.status_code})")
        except Exception as e:
            print(f"  × 异常: {e}")

if __name__ == "__main__":
    print("SN号重复提交检测功能测试")
    print("=" * 50)
    
    # 基本功能测试
    test_check_serial_number()
    
    # 不同场景测试
    test_different_scenarios()
    
    print("\n测试完成")
