-- 执行前务必备份数据！建议先在测试环境验证！
UPDATE cpu_table
SET m_area_test_log = JSON_OBJECT(
  -- 保留原有的 4 个字段值
  'test_time', JSON_EXTRACT(m_area_test_log, '$.test_time'),  -- 保留原 test_time
  'system_info', JSON_OBJECT(
    'session_id', JSON_EXTRACT(m_area_test_log, '$.system_info.session_id'),  -- 保留原 session_id
    'mac_address', JSON_EXTRACT(m_area_test_log, '$.system_info.mac_address'),  -- 保留原 mac_address
    'device_serial', JSON_EXTRACT(m_area_test_log, '$.system_info.device_serial'),  -- 保留原 device_serial
    -- 新 system_info 中的其他字段（替换为新内容）
    'device_ip', '*************',
    'test_type', 'auto_test_only',
    'scan_time_ms', 11,
    'test_duration_ms', 0
  ),
  -- 新内容：替换其他所有字段
  'details', JSON_ARRAY(
    JSON_OBJECT(
      'test', 'RS485_通信',
      'logic', 'M0+M1 (AND组合)',
      'expect', '所有M区值都为1: M0=1 AND M1=1',
      'reason', '所有M区值都为1',
      'result', 'OK',
      'values', 'M0=1, M1=1',
      'test_code', 'rs485_1',
      'test_mode', 'auto_test'
    ),
    JSON_OBJECT(
      'test', 'RS232通信',
      'logic', 'M2 (单值判断)',
      'expect', 'M2=1 (单值条件)',
      'reason', 'M2=1 (通过)',
      'result', 'OK',
      'values', 'M1',
      'test_code', 'rs232',
      'test_mode', 'auto_test'
    ),
    JSON_OBJECT(
      'test', 'CANbus通信',
      'logic', 'M3 (单值判断)',
      'expect', 'M3=1 (单值条件)',
      'reason', 'M3=1 (通过)',
      'result', 'OK',
      'values', 'M1',
      'test_code', 'canbus',
      'test_mode', 'auto_test'
    ),
    JSON_OBJECT(
      'test', 'EtherCAT通信',
      'logic', 'M4 (单值判断)',
      'expect', 'M4=1 (单值条件)',
      'reason', 'M4=1 (通过)',
      'result', 'OK',
      'values', 'M1',
      'test_code', 'ethercat',
      'test_mode', 'auto_test'
    ),
    JSON_OBJECT(
      'test', 'Backplane Bus通信',
      'logic', '双重检查: M5=1 + 模块消息检查',
      'expect', 'M5=1 且存在slave=1模块',
      'reason', 'M区检查通过 + 模块消息检查通过',
      'result', 'OK',
      'values', 'M5=1',
      'test_code', 'backplane_bus',
      'test_mode', 'auto_test',
      'check_type', 'dual_verification',
      'step1_result', 'PASS',
      'step2_result', 'PASS'
    )
  ),
  'summary', JSON_OBJECT(
    'failed', 0,
    'passed', 5,
    'result', 'OK',
    'controlled_tests', 5
  ),
  'm_values', JSON_ARRAY(1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  'test_mode', JSON_OBJECT(
    'mode_type', 'auto_test',
    'auto_test_completed', TRUE,
    'visual_test_completed', FALSE
  ),
  'm_data_raw', '1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0',
  'product_config', 'All'
)
-- 指定需要更新的 pro_sn 列表（与步骤1中的列表一致）
WHERE pro_sn IN (
'MC501CPU2025290050',  -- 替换为实际 pro_sn
'MC501CPU2025290032',
'MC501CPU2025290020',   -- 最后一个 pro_sn
"MC501CPU2025290107",
"MC501CPU2025290164",
"MC501CPU2025290005",
"MC501CPU2025290051",
"MC501CPU2025290119",
"MC501CPU2025290080",
"MC501CPU2025290093",
"MC501CPU2025290033",
"MC501CPU2025290026",
"MC501CPU2025290153",
"MC501CPU2025290177",
"MC501CPU2025290088",
"MC501CPU2025290054",
"MC501CPU2025290198",
"MC501CPU2025290064",
"MC501CPU2025290043",
"MC501CPU2025290171",
"MC501CPU2025290104",
"MC501CPU2025290142",
"MC501CPU2025290003",
"MC501CPU2025290098",
"MC501CPU2025290114",
"MC501CPU2025290130",
"MC501CPU2025290149",
"MC501CPU2025290145",
"MC501CPU2025290128",
"MC501CPU2025290129",
"MC501CPU2025290019",
"MC501CPU2025290111",
"MC501CPU2025290096",
"MC501CPU2025290045",
"MC501CPU2025290105",
"MC501CPU2025290097",
"MC501CPU2025290136",
"MC501CPU2025290085",
"MC501CPU2025290082",
"MC501CPU2025290028",
"MC501CPU2025290039",
"MC501CPU2025290169",
"MC501CPU2025290159",
"MC501CPU2025290087",
"MC501CPU2025290012",
"MC501CPU2025290014",
"MC501CPU2025290151",
"MC501CPU2025290160",
"MC501CPU2025290079",
"MC501CPU2025290004",
"MC501CPU2025290037",
"MC501CPU2025290150",
"MC501CPU2025290163",
"MC501CPU2025290047",
"MC501CPU2025290158",
"MC501CPU2025290161",
"MC501CPU2025290099",
"MC501CPU2025290007",
"MC501CPU2025290183",
"MC501CPU2025290176",
"MC501CPU2025290021"
);